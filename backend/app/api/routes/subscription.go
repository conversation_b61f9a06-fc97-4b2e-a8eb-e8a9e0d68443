package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/pkg/domains/subscription"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
)

func SubscriptionRoutes(r *gin.RouterGroup, s subscription.Service) {
	g := r.Group("/subscription")

	// Public webhook endpoint (no auth required)
	g.POST("/webhook/revenuecat", RevenueCatWebhook(s))
	// Protected endpoints
	g.Use(middleware.Authorized())
	g.GET("/status", GetSubscriptionStatus(s))
	g.POST("/activate", ActivateSubscription(s))              // For testing/manual activation
	g.POST("/purchase-notification", PurchaseNotification(s)) // From frontend after purchase
}

// @Summary RevenueCat Webhook
// @Description Handle RevenueCat webhook events
// @Tags subscription
// @Accept json
// @Produce json
// @Param request body dtos.RevenueCatWebhookRequest true "RevenueCat webhook data"
// @Success 200 {object} map[string]interface{} "Webhook processed successfully"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /subscription/webhook/revenuecat [post]
func RevenueCatWebhook(s subscription.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var webhook dtos.RevenueCatWebhookRequest
		if err := c.ShouldBindJSON(&webhook); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		// Process webhook
		err := s.ProcessRevenueCatWebhook(&webhook)
		if err != nil {
			// Log error but return 200 to prevent RevenueCat retries for invalid data
			c.JSON(http.StatusOK, gin.H{
				"message": "Webhook received but processing failed",
				"error":   err.Error(),
				"status":  http.StatusOK,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Webhook processed successfully",
			"status":  http.StatusOK,
		})
	}
}

// @Summary Get Subscription Status
// @Description Get current user's subscription status
// @Tags subscription
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dtos.SubscriptionStatusResponse "Subscription status"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /subscription/status [get]
func GetSubscriptionStatus(s subscription.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)

		status, err := s.GetUserSubscriptionStatus(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  err.Error(),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   status,
			"status": http.StatusOK,
		})
	}
}

// @Summary Activate Subscription
// @Description Manually activate a subscription (for testing)
// @Tags subscription
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.ActivateSubscriptionRequest true "Subscription activation data"
// @Success 200 {object} map[string]interface{} "Subscription activated"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /subscription/activate [post]
func ActivateSubscription(s subscription.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.ActivateSubscriptionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		// Use current user ID if not provided
		userID := state.GetCurrentUserID(c)
		if req.UserID == "" {
			req.UserID = userID.String()
		}

		err := s.ActivateSubscription(req.UserID, req.ProductID, req.OriginalTransactionID, req.ExpiresDate, req.RevenueCatCustomerID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Subscription activated successfully",
			"status":  http.StatusOK,
		})
	}
}

// @Summary Purchase Notification
// @Description Handle purchase notification from frontend after RevenueCat purchase
// @Tags subscription
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.RevenueCatPurchaseNotification true "Purchase notification data"
// @Success 200 {object} map[string]interface{} "Purchase processed"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /subscription/purchase-notification [post]
func PurchaseNotification(s subscription.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var notification dtos.RevenueCatPurchaseNotification
		if err := c.ShouldBindJSON(&notification); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentUserID(c)

		// Process the purchase notification and update user's RevenueCat ID
		err := s.ProcessPurchaseNotification(userID.String(), &notification)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  err.Error(),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Purchase notification processed",
			"status":  http.StatusOK,
		})
	}
}
