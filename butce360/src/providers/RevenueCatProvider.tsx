import React, { createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import Purchases from 'react-native-purchases';
import { subscriptionService } from '../services/subscriptionService';
import { useAuth } from '../context/AuthContext';

interface RevenueCatContextProps {
  customerInfo: any; // CustomerInfo tipi kullanabilirsin ama any de çalışır
  offerings: any; // Offerings tipi yerine any
  purchasePackage: (pkg: any) => Promise<{ success: boolean; cancelled?: boolean; error?: any; customer?: any }>;
  restorePurchases: () => Promise<{ success: boolean; error?: any; customer?: any }>;
  isLoading: boolean;
}

const RevenueCatContext = createContext<RevenueCatContextProps | undefined>(undefined);

export const RevenueCatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [customerInfo, setCustomerInfo] = useState<any>(null);
  const [offerings, setOfferings] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { state: authState } = useAuth();

  useEffect(() => {
    console.log('[RevenueCat] provider useEffect called');
    let listener: any;

    const setup = async () => {
      console.log('[RevenueCat] Setting up...');
      // RevenueCat'in zaten configure edilip edilmediğini kontrol et
      try {
        await Purchases.getCustomerInfo();
        console.log('[RevenueCat] Already configured, skipping setup');
        // Zaten configure edilmiş, sadece offerings'i al
        try {
          console.log('[RevenueCat] Fetching offerings...');
          const o = await Purchases.getOfferings();
          console.log('[RevenueCat] Offerings fetched:', o);
          setOfferings(o);
        } catch (err) {
          console.warn('Offerings error', err);
        }
        return;
      } catch (error) {
        // Configure edilmemiş, normal setup'a devam et
        console.log('[RevenueCat] Not configured yet, proceeding with setup');
      }

      Purchases.setLogLevel(Purchases.LOG_LEVEL.DEBUG);

      const apiKey = Platform.OS === 'ios'
        ? 'appl_rlBgGtsddRFHtbdsQABoTsiPKCZ'
        : 'goog_YOUR_ANDROID_API_KEY_HERE'; // Android API key'ini RevenueCat dashboard'dan alın

      await Purchases.configure({ apiKey });

      // Set user ID if authenticated
      if (authState.isAuthenticated && authState.user?.id) {
        try {
          console.log('[RevenueCat] Setting user ID:', authState.user.id);
          await Purchases.logIn(authState.user.id);
        } catch (loginError) {
          console.warn('[RevenueCat] Failed to set user ID:', loginError);
        }
      }

      try {
        const ci = await Purchases.getCustomerInfo();
        setCustomerInfo(ci);
      } catch (err) {
        console.warn('CustomerInfo error', err);
      }

      try {
        const o = await Purchases.getOfferings();
        setOfferings(o);
      } catch (err) {
        console.warn('Offerings error', err);
      }

      listener = Purchases.addCustomerInfoUpdateListener(setCustomerInfo);
      setIsLoading(false);
    };

    setup();

    return () => {
      if (listener) listener.remove(); // cleanup
    };
  }, [authState.isAuthenticated, authState.user?.id]);

  const purchasePackage = async (pkg: any) => {
    try {
      console.log('[RevenueCat] Starting purchase for package:', pkg.product.identifier);
      const result = await Purchases.purchasePackage(pkg);
      const customer = result.customerInfo;
      setCustomerInfo(customer);

      // Notify backend about the purchase
      try {
        // Get the active premium entitlement to extract transaction details
        const premiumEntitlement = customer?.entitlements?.active?.premium;
        const originalTransactionId = customer.originalAppUserId || '';
        const transactionId = ''; // Will be filled by backend from webhook
        const expirationDate = premiumEntitlement?.expirationDate ? new Date(premiumEntitlement.expirationDate) : new Date(Date.now() + (pkg.product.identifier.includes('year') ? 365 : 30) * 24 * 60 * 60 * 1000);

        await subscriptionService.notifyPurchase({
          customerInfo: customer,
          productId: pkg.product.identifier,
          originalTransactionId,
          transactionId,
          purchaseDate: new Date(),
          expirationDate,
        });
        console.log('[RevenueCat] Backend notified about purchase');
      } catch (notifyError) {
        console.warn('[RevenueCat] Failed to notify backend about purchase:', notifyError);
        // Don't fail the purchase if backend notification fails
      }

      return { success: true, customer };
    } catch (e: any) {
      console.error('[RevenueCat] Purchase failed:', e);
      if (e.userCancelled) return { success: false, cancelled: true };
      return { success: false, error: e };
    }
  };

  const restorePurchases = async () => {
    try {
      console.log('[RevenueCat] Starting restore purchases');
      const customer = await Purchases.restorePurchases();
      setCustomerInfo(customer);

      // If user has active premium entitlement after restore, notify backend
      const hasActivePremium = !!customer?.entitlements?.active?.premium;
      if (hasActivePremium) {
        try {
          // Find the active subscription to get product details
          const activeEntitlements = customer?.entitlements?.active || {};
          const premiumEntitlement = activeEntitlements.premium;

          if (premiumEntitlement) {
            await subscriptionService.notifyPurchase({
              customerInfo: customer,
              productId: premiumEntitlement.productIdentifier || 'premium',
              originalTransactionId: customer.originalAppUserId || '',
              transactionId: '',
              purchaseDate: new Date(premiumEntitlement.originalPurchaseDate || Date.now()),
              expirationDate: new Date(premiumEntitlement.expirationDate || Date.now() + 365 * 24 * 60 * 60 * 1000),
            });
            console.log('[RevenueCat] Backend notified about restored purchase');
          }
        } catch (notifyError) {
          console.warn('[RevenueCat] Failed to notify backend about restored purchase:', notifyError);
          // Don't fail the restore if backend notification fails
        }
      }

      console.log('[RevenueCat] Restore purchases completed successfully');
      return { success: true, customer };
    } catch (e: any) {
      console.error('[RevenueCat] Restore purchases failed:', e);
      return { success: false, error: e };
    }
  };

  return (
    <RevenueCatContext.Provider value={{ customerInfo, offerings, purchasePackage, restorePurchases, isLoading }}>
      {children}
    </RevenueCatContext.Provider>
  );
};

export const useRevenueCat = (): RevenueCatContextProps => {
  const context = useContext(RevenueCatContext);
  if (!context) throw new Error('useRevenueCat must be used within a RevenueCatProvider');
  return context;
};
