import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useThemedColors } from '../hooks/useThemedStyles';
import { useAuth } from '../context/AuthContext';

// Screens
import HomeScreen from '../screens/Home/HomeScreen';
import AddTransactionScreen from '../screens/Transactions/AddTransactionScreen';
import MenuStackNavigator from './MenuStack';
import BudgetStackNavigator from './BudgetStack';
import AddBudgetScreen from '../screens/Budget/AddBudgetScreen';
import CategoriesScreen from '../screens/Categories/CategoriesScreen';
import CategoryDetailScreen from '../screens/Categories/CategoryDetailScreen';
import AddCategoryScreen from '../screens/Categories/AddCategoryScreen';
import AccountsScreen from '../screens/Accounts/AccountsScreen';
import AddAccountScreen from '../screens/Accounts/AddAccountScreen';
import AccountDetailScreen from '../screens/Accounts/AccountDetailScreen';
import ReportsScreen from '../screens/Reports/ReportsScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';
import ThemeScreen from '../screens/Theme/ThemeScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import EditProfileScreen from '../screens/Profile/EditProfileScreen';
import ProfileDetailsScreen from '../screens/Profile/ProfileDetailsScreen';
import ChangePasswordScreen from '../screens/Profile/ChangePasswordScreen';
import AboutScreen from '../screens/About/AboutScreen';
import HelpScreen from '../screens/Help/HelpScreen';
import TransactionsScreen from '../screens/Transactions/TransactionsScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import RegisterScreen from '../screens/Auth/RegisterScreen';
import BankStatementScreen from '../screens/BankStatement/BankStatementScreen';
import ExportScreen from '../screens/Export/ExportScreen';
import PaywallScreen from '../screens/Paywall/PaywallScreen';
import InvestmentStackNavigator from './InvestmentStack';
import TabBarAdapter from './TabBarAdapter';
import NavigationHeader from '../components/common/NavigationHeader';
import type { BottomTabBarProps } from '@react-navigation/bottom-tabs';

// Stable tab bar renderer to satisfy React Navigation's expected type
const renderTabBar = (props: BottomTabBarProps) => <TabBarAdapter {...props} />;

// Reusable header components
interface HeaderProps {
  navigation: any;
  back?: any;
  route?: any;
}

const LoginHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Giriş" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const RegisterHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Kayıt" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const CategoriesHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader 
    title="Kategoriler" 
    showBackButton={!!back} 
    onBackPress={navigation.goBack}
    rightButtonText="Ekle"
    onRightButtonPress={() => navigation.navigate('AddCategory')}
  />
);

const CategoryDetailHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Kategori Detayı" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const AccountsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader 
    title="Hesaplar" 
    showBackButton={!!back} 
    onBackPress={navigation.goBack}
    rightButtonText="Ekle"
    onRightButtonPress={() => navigation.navigate('AddAccount')}
  />
);

const AccountDetailsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Hesap Detayı" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const ReportsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Raporlar" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const SettingsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Ayarlar" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const ThemeHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Tema" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const ProfileHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Profil" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const AboutHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Hakkında" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const HelpHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Yardım" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const TransactionsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="İşlemler" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const AddBudgetHeader = ({ navigation, back, route }: HeaderProps) => (
  <NavigationHeader 
    title={(route as any)?.params?.budgetId ? 'Bütçe Düzenle' : 'Yeni Bütçe'} 
    showBackButton={!!back} 
    onBackPress={navigation.goBack} 
  />
);

const AddCategoryHeader = ({ navigation, back, route }: HeaderProps) => (
  <NavigationHeader 
    title={(route as any)?.params?.categoryId ? 'Kategori Düzenle' : 'Kategori Ekle'} 
    showBackButton={!!back} 
    onBackPress={navigation.goBack} 
  />
);

const AddAccountHeader = ({ navigation, back, route }: HeaderProps) => (
  <NavigationHeader 
    title={(route as any)?.params?.accountId ? 'Hesap Düzenle' : 'Hesap Ekle'} 
    showBackButton={!!back} 
    onBackPress={navigation.goBack} 
  />
);

const ProfileDetailsHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Profil Bilgileri" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const EditProfileHeader = ({ navigation, back, route }: HeaderProps) => (
  <NavigationHeader
    title="Profili Düzenle"
    showBackButton={!!back}
    onBackPress={navigation.goBack}
    rightButtonText="Kaydet"
    onRightButtonPress={() => (route as any).params?.onSave?.()}
  />
);

const ChangePasswordHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Şifre Değiştir" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const BankStatementHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Banka Ekstresi" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const ExportHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Dışa Aktar" showBackButton={!!back} onBackPress={navigation.goBack} />
);

const PaywallHeader = ({ navigation, back }: HeaderProps) => (
  <NavigationHeader title="Premium" showBackButton={!!back} onBackPress={navigation.goBack} />
);

type RootStackParamList = {
  home: undefined;
  login: undefined;
  register: undefined;
  add: undefined;
  investment: undefined;
  menu: undefined;
  budget: undefined;
  categories: undefined;
  accounts: undefined;
  reports: undefined;
  settings: undefined;
  theme: undefined;
  profile: undefined;
  about: undefined;
  help: undefined;
  transactions: undefined;
  addBudget: undefined;
  AddCategory: undefined;
  AddAccount: undefined;
  AccountDetails: undefined;
  CategoryDetail: undefined;
  ProfileDetails: undefined;
  EditProfile: undefined;
  changePassword: undefined;
  bankStatement: undefined;
  export: undefined;
  Paywall: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

type TabParamList = {
  home: undefined;
  add: undefined;
  budget: undefined;
  investment: undefined;
  menu: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

// Wrapper components to avoid inline render props
const AddTransactionWrapper: React.FC<any> = (props) => (
  <AddTransactionScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const CategoriesWrapper: React.FC<any> = (props) => (
  <CategoriesScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const CategoryDetailWrapper: React.FC<any> = (props) => (
  <CategoryDetailScreen 
    categoryId={(props.route?.params as any)?.id}
    categoryName={(props.route?.params as any)?.name}
    onNavigate={(s) => legacyNavigate(props.navigation, s)}
    onGoBack={props.navigation.goBack}
  />
);
const AccountsWrapper: React.FC<any> = (props) => (
  <AccountsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AccountDetailWrapper: React.FC<any> = (props) => (
  <AccountDetailScreen accountId={(props.route?.params as any)?.id} onNavigate={(s) => legacyNavigate(props.navigation, s)} onGoBack={props.navigation.goBack} />
);
const SettingsWrapper: React.FC<any> = (props) => (
  <SettingsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ProfileWrapper: React.FC<any> = (props) => (
  <ProfileScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AddBudgetWrapper: React.FC<any> = (props) => (
  <AddBudgetScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AddCategoryWrapper: React.FC<any> = (props) => (
  <AddCategoryScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AddAccountWrapper: React.FC<any> = (props) => (
  <AddAccountScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ProfileDetailsWrapper: React.FC<any> = (props) => (
  <ProfileDetailsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const EditProfileWrapper: React.FC<any> = (props) => (
  <EditProfileScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const BankStatementWrapper: React.FC<any> = (props) => (
  <BankStatementScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ExportWrapper: React.FC<any> = (props) => (
  <ExportScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);

// Legacy onNavigate adapter: supports plain names and query-like params e.g. "CategoryDetail?id=..&name=.."
const legacyNavigate = (navigation: any, screen: string) => {
  try {
    if (!screen) return;
    // Route legacy single-screen names to nested stacks to keep tab bar visible
    if (screen === 'addBudget') {
      return navigation.navigate('budget' as any, { screen: 'AddBudget' });
    }
    const qIndex = screen.indexOf('?');
    if (qIndex === -1) {
      if (screen === 'MainTabs') return navigation.navigate('home');
      if (screen === 'Welcome') return navigation.navigate('login');
      return navigation.navigate(screen);
    }
    const base = screen.substring(0, qIndex);
    const query = screen.substring(qIndex + 1);
    const params: Record<string, any> = {};
    query.split('&').forEach(pair => {
      const [k, v] = pair.split('=');
      if (k) params[k] = decodeURIComponent(v || '');
    });
    return navigation.navigate(base, params);
  } catch (e) {
    // Navigation failed silently
  }
};

const TabNavigator: React.FC = () => (
  <Tab.Navigator
    screenOptions={{ headerShown: false, tabBarShowLabel: false }}
    tabBar={renderTabBar}
  >
    <Tab.Screen name="home" component={HomeScreen} />
    <Tab.Screen name="add" component={AddTransactionWrapper} />
    <Tab.Screen name="budget" component={BudgetStackNavigator} />
    <Tab.Screen name="investment" component={InvestmentStackNavigator} />
    <Tab.Screen
      name="menu"
      component={MenuStackNavigator}
      listeners={({ navigation }) => ({
        tabPress: () => {
          // Ensure Menu stack opens at root when tab is pressed again
          navigation.navigate('menu' as any, { screen: 'MenuHome' });
        },
        focus: () => {
          // Whenever Menu tab gains focus, reset to MenuHome
          navigation.navigate('menu' as any, { screen: 'MenuHome' });
        }
      })}
    />
  </Tab.Navigator>
);

const MainNavigator: React.FC = () => {
  const { state } = useAuth();
  const colors = useThemedColors();

  const screenOptions = {
    headerShown: false,
    contentStyle: { backgroundColor: colors.background.primary },
  } as const;

  const getInitialRouteName = () => {
    if (!state.isAuthenticated && !state.isGuest) return 'login';
    return 'home';
  };

  
  return (
    <Stack.Navigator initialRouteName={getInitialRouteName()} screenOptions={screenOptions}>
      {/* Auth */}
      <Stack.Screen name="login" component={LoginScreen} options={{ header: LoginHeader }} />
      <Stack.Screen name="register" component={RegisterScreen} options={{ header: RegisterHeader }} />

      {/* Tabs */}
      <Stack.Screen name="home" component={TabNavigator} options={{ headerShown: false }} />

      {/* Other Screens */}
      <Stack.Screen name="categories" component={CategoriesWrapper} options={{ header: CategoriesHeader }} />
      <Stack.Screen name="CategoryDetail" component={CategoryDetailWrapper} options={{ header: CategoryDetailHeader }} />
      <Stack.Screen name="accounts" component={AccountsWrapper} options={{ header: AccountsHeader }} />
      <Stack.Screen name="AccountDetails" component={AccountDetailWrapper} options={{ header: AccountDetailsHeader }} />
      <Stack.Screen name="reports" component={ReportsScreen} options={{ header: ReportsHeader }} />
      <Stack.Screen name="settings" component={SettingsWrapper} options={{ header: SettingsHeader }} />
      <Stack.Screen name="theme" component={ThemeScreen} options={{ header: ThemeHeader }} />
      <Stack.Screen name="profile" component={ProfileWrapper} options={{ header: ProfileHeader }} />
      <Stack.Screen name="about" component={AboutScreen} options={{ header: AboutHeader }} />
      <Stack.Screen name="help" component={HelpScreen} options={{ header: HelpHeader }} />
      <Stack.Screen name="transactions" component={TransactionsScreen} options={{ header: TransactionsHeader }} />
      <Stack.Screen name="addBudget" component={AddBudgetWrapper} options={{ header: AddBudgetHeader }} />
      <Stack.Screen name="AddCategory" component={AddCategoryWrapper} options={{ header: AddCategoryHeader }} />
      <Stack.Screen name="AddAccount" component={AddAccountWrapper} options={{ header: AddAccountHeader }} />
      <Stack.Screen name="ProfileDetails" component={ProfileDetailsWrapper} options={{ header: ProfileDetailsHeader }} />
      <Stack.Screen name="EditProfile" component={EditProfileWrapper} options={{ header: EditProfileHeader }} />
      <Stack.Screen name="changePassword" component={ChangePasswordScreen} options={{ header: ChangePasswordHeader }} />
      <Stack.Screen name="bankStatement" component={BankStatementWrapper} options={{ header: BankStatementHeader }} />
      <Stack.Screen name="export" component={ExportWrapper} options={{ header: ExportHeader }} />
      <Stack.Screen name="Paywall" component={PaywallScreen} options={{ header: PaywallHeader }} />
    </Stack.Navigator>
  );
};

export default MainNavigator;
