package routes

import (
	"bytes"
	"io"
	"mime/multipart"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/domains/bank_statement"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
)

func BankStatementRoutes(r *gin.RouterGroup, s bank_statement.Service, authService auth.Service) {
	g := r.Group("/bank-statements")
	g.Use(middleware.Authorized())
	g.Use(middleware.RequiresPremium(authService)) // Premium required for bank statement features

	g.POST("/upload", UploadBankStatement(s))
	g.POST("/import", ImportBankStatementEntries(s))
}

// @Summary Upload bank statement
// @Description Upload a bank statement PDF and forward it to the extraction service
// @Tags bank-statements
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param file formData file true "Bank statement PDF file"
// @Success 200 {object} map[string]interface{} "Returns extracted transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /bank-statements/upload [post]
func UploadBankStatement(_ bank_statement.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		fileHeader, err := c.FormFile("file")
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "No file uploaded or invalid file",
				"status": 400,
			})
			return
		}

		// Check file extension
		if !isValidPDFFile(fileHeader.Filename) {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Only PDF files are allowed",
				"status": 400,
			})
			return
		}

		fileContent, err := fileHeader.Open()
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Failed to open file: " + err.Error(),
				"status": 400,
			})
			return
		}
		defer fileContent.Close()

		var buf bytes.Buffer
		writer := multipart.NewWriter(&buf)
		fileWriter, err := writer.CreateFormFile("data0", fileHeader.Filename)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{
				"error":  "Failed to prepare request: " + err.Error(),
				"status": 500,
			})
			return
		}

		if _, err := io.Copy(fileWriter, fileContent); err != nil {
			c.AbortWithStatusJSON(500, gin.H{
				"error":  "Failed to read file: " + err.Error(),
				"status": 500,
			})
			return
		}

		if err := writer.Close(); err != nil {
			c.AbortWithStatusJSON(500, gin.H{
				"error":  "Failed to finalize request: " + err.Error(),
				"status": 500,
			})
			return
		}

		req, err := http.NewRequestWithContext(c.Request.Context(), http.MethodPost, "https://n8n.butce360.com/webhook/extract-tx", &buf)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{
				"error":  "Failed to build request: " + err.Error(),
				"status": 500,
			})
			return
		}

		req.Header.Set("Content-Type", writer.FormDataContentType())
		req.Header.Set("client_id", "nocytech")

		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			c.AbortWithStatusJSON(502, gin.H{
				"error":  "Failed to call extraction service: " + err.Error(),
				"status": 502,
			})
			return
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			c.AbortWithStatusJSON(502, gin.H{
				"error":  "Failed to read extraction response: " + err.Error(),
				"status": 502,
			})
			return
		}

		c.Data(resp.StatusCode, "application/json", body)
	}
}

// @Summary Import bank statement entries
// @Description Import selected bank statement entries as transactions
// @Tags bank-statements
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body []dtos.BankStatementImportRequest true "Bank statement entries to import"
// @Success 200 {object} map[string]interface{} "Returns created transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /bank-statements/import [post]
func ImportBankStatementEntries(s bank_statement.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Parse request body
		var req []dtos.BankStatementImportRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Validate request
		if len(req) == 0 {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "No entries to import",
				"status": 400,
			})
			return
		}

		// Import entries
		transactions, err := s.ImportBankStatementEntries(userID.String(), req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Return the created transactions
		c.JSON(200, gin.H{
			"data": dtos.BankStatementImportResponse{
				Transactions: transactions,
			},
			"status": 200,
		})
	}
}

// isValidPDFFile checks if the file has a PDF extension
func isValidPDFFile(filename string) bool {
	return len(filename) > 4 && filename[len(filename)-4:] == ".pdf"
}
