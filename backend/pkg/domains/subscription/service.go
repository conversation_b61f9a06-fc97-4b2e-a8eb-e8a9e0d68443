package subscription

import (
	"fmt"
	"time"

	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
)

type Service interface {
	ProcessRevenueCatWebhook(webhook *dtos.RevenueCatWebhookRequest) error
	GetUserSubscriptionStatus(userID string) (*dtos.SubscriptionStatusResponse, error)
	ActivateSubscription(userID, productID, originalTransactionID string, expiresDate time.Time, revenueCatCustomerID string) error
	DeactivateSubscription(userID string) error
	SyncUserPremiumStatus(userID string) error
	ProcessPurchaseNotification(userID string, notification *dtos.RevenueCatPurchaseNotification) error
}

type service struct {
	repository  Repository
	authService auth.Service
}

func NewService(repository Repository, authService auth.Service) Service {
	return &service{
		repository:  repository,
		authService: authService,
	}
}

func (s *service) ProcessRevenueCatWebhook(webhook *dtos.RevenueCatWebhookRequest) error {
	fmt.Printf("[Webhook] Processing RevenueCat webhook: Type=%s, AppUserID=%s, ProductID=%s\n",
		webhook.Event.Type, webhook.Event.AppUserID, webhook.Event.ProductID)

	// Find user by RevenueCat customer ID
	user, err := s.authService.GetUserByRevenueCatID(webhook.Event.AppUserID)
	if err != nil {
		// If user not found by RevenueCat ID, try to find by user ID (for backward compatibility)
		user, err = s.authService.GetUserByID(webhook.Event.AppUserID)
		if err != nil {
			fmt.Printf("[Webhook] User not found for RevenueCat ID %s: %v\n", webhook.Event.AppUserID, err)
			return fmt.Errorf("user not found for RevenueCat ID %s: %w", webhook.Event.AppUserID, err)
		}

		// Update user's RevenueCat ID for future webhooks
		user.RevenueCatID = &webhook.Event.AppUserID
		if updateErr := s.authService.UpdateUser(user); updateErr != nil {
			fmt.Printf("[Webhook] Failed to update user RevenueCat ID: %v\n", updateErr)
		}
	}

	switch webhook.Event.Type {
	case "INITIAL_PURCHASE", "RENEWAL":
		return s.handlePurchaseEvent(user, webhook)
	case "CANCELLATION", "EXPIRATION":
		return s.handleCancellationEvent(user, webhook)
	case "BILLING_ISSUE":
		return s.handleBillingIssue(user, webhook)
	default:
		// Log unknown webhook type but don't fail
		fmt.Printf("Unknown webhook type: %s\n", webhook.Event.Type)
		return nil
	}
}

func (s *service) handlePurchaseEvent(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	// Extract subscription data from webhook
	productID := webhook.Event.ProductID
	originalTransactionID := webhook.Event.OriginalTransactionID
	expiresDate := webhook.Event.ExpirationDate

	// Check if subscription already exists
	existingSubscription, err := s.repository.GetSubscriptionByOriginalTransactionID(originalTransactionID)
	if err != nil {
		return err
	}

	if existingSubscription != nil {
		// Update existing subscription
		existingSubscription.UpdateFromWebhook(webhook.Event.Type, expiresDate, true, true)
		existingSubscription.TransactionID = webhook.Event.TransactionID
		err = s.repository.UpdateSubscription(existingSubscription)
	} else {
		// Create new subscription
		subscription := &entities.Subscription{
			UserID:                user.ID.String(),
			RevenueCatCustomerID:  webhook.Event.AppUserID,
			ProductID:             productID,
			OriginalTransactionID: originalTransactionID,
			TransactionID:         webhook.Event.TransactionID,
			PurchaseDate:          webhook.Event.PurchaseDate,
			ExpiresDate:           expiresDate,
			IsActive:              true,
			AutoRenewStatus:       true,
			Environment:           webhook.Event.Environment,
			Store:                 webhook.Event.Store,
			LastWebhookType:       webhook.Event.Type,
			LastWebhookDate:       time.Now(),
		}
		err = s.repository.CreateSubscription(subscription)
	}

	if err != nil {
		return err
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) handleCancellationEvent(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	subscription, err := s.repository.GetSubscriptionByOriginalTransactionID(webhook.Event.OriginalTransactionID)
	if err != nil {
		return err
	}

	if subscription != nil {
		subscription.UpdateFromWebhook(webhook.Event.Type, webhook.Event.ExpirationDate, false, false)
		err = s.repository.UpdateSubscription(subscription)
		if err != nil {
			return err
		}
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) handleBillingIssue(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	subscription, err := s.repository.GetSubscriptionByOriginalTransactionID(webhook.Event.OriginalTransactionID)
	if err != nil {
		return err
	}

	if subscription != nil {
		subscription.UpdateFromWebhook(webhook.Event.Type, webhook.Event.ExpirationDate, false, subscription.AutoRenewStatus)
		err = s.repository.UpdateSubscription(subscription)
		if err != nil {
			return err
		}
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) GetUserSubscriptionStatus(userID string) (*dtos.SubscriptionStatusResponse, error) {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		// If user not found, return default free status instead of error
		return &dtos.SubscriptionStatusResponse{
			IsPremium:     false,
			Plan:          "free",
			ExpiresAt:     nil,
			RemainingDays: 0,
		}, nil
	}

	subscription, err := s.repository.GetActiveSubscriptionByUserID(userID)
	if err != nil {
		// If subscription query fails, return user's current premium status
		return &dtos.SubscriptionStatusResponse{
			IsPremium:     user.IsPremium(),
			Plan:          user.Plan,
			ExpiresAt:     user.PremiumExpiresAt,
			RemainingDays: 0,
		}, nil
	}

	response := &dtos.SubscriptionStatusResponse{
		IsPremium:     user.IsPremium(),
		Plan:          user.Plan,
		ExpiresAt:     user.PremiumExpiresAt,
		RemainingDays: user.GetPremiumRemainingDays(),
	}

	if subscription != nil {
		response.ProductID = subscription.ProductID
		response.AutoRenewStatus = subscription.AutoRenewStatus
		response.Store = subscription.Store
	}

	return response, nil
}

func (s *service) ActivateSubscription(userID, productID, originalTransactionID string, expiresDate time.Time, revenueCatCustomerID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Create or update subscription
	existingSubscription, err := s.repository.GetSubscriptionByOriginalTransactionID(originalTransactionID)
	if err != nil {
		return err
	}

	if existingSubscription != nil {
		existingSubscription.ExpiresDate = expiresDate
		existingSubscription.IsActive = true
		existingSubscription.AutoRenewStatus = true
		err = s.repository.UpdateSubscription(existingSubscription)
	} else {
		subscription := &entities.Subscription{
			UserID:                userID,
			RevenueCatCustomerID:  revenueCatCustomerID,
			ProductID:             productID,
			OriginalTransactionID: originalTransactionID,
			PurchaseDate:          time.Now(),
			ExpiresDate:           expiresDate,
			IsActive:              true,
			AutoRenewStatus:       true,
			Environment:           "PRODUCTION", // Default to production
			Store:                 "APP_STORE",  // Default to App Store
		}
		err = s.repository.CreateSubscription(subscription)
	}

	if err != nil {
		return err
	}

	// Update user's premium status
	user.ActivatePremium(expiresDate, revenueCatCustomerID)
	return s.authService.UpdateUser(user)
}

func (s *service) DeactivateSubscription(userID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Deactivate user's premium
	user.DeactivatePremium()
	return s.authService.UpdateUser(user)
}

func (s *service) SyncUserPremiumStatus(userID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	activeSubscription, err := s.repository.GetActiveSubscriptionByUserID(userID)
	if err != nil {
		return err
	}

	if activeSubscription != nil && activeSubscription.IsValidAndActive() {
		// User has active subscription
		user.ActivatePremium(activeSubscription.ExpiresDate, activeSubscription.RevenueCatCustomerID)
	} else {
		// No active subscription
		user.DeactivatePremium()
	}

	return s.authService.UpdateUser(user)
}

func (s *service) ProcessPurchaseNotification(userID string, notification *dtos.RevenueCatPurchaseNotification) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Extract RevenueCat customer ID from customerInfo
	var revenueCatCustomerID string
	if notification.CustomerInfo != nil {
		if customerInfoMap, ok := notification.CustomerInfo.(map[string]interface{}); ok {
			if originalAppUserId, exists := customerInfoMap["originalAppUserId"]; exists {
				if id, ok := originalAppUserId.(string); ok {
					revenueCatCustomerID = id
				}
			}
		}
	}

	// Update user's RevenueCat ID if not already set
	if user.RevenueCatID == nil || *user.RevenueCatID != revenueCatCustomerID {
		if revenueCatCustomerID != "" {
			user.RevenueCatID = &revenueCatCustomerID
			if err := s.authService.UpdateUser(user); err != nil {
				fmt.Printf("[PurchaseNotification] Failed to update user RevenueCat ID: %v\n", err)
			}
		}
	}

	// Create or update subscription record
	existingSubscription, err := s.repository.GetSubscriptionByOriginalTransactionID(notification.OriginalTransactionID)
	if err != nil {
		return err
	}

	if existingSubscription != nil {
		// Update existing subscription
		existingSubscription.ExpiresDate = notification.ExpirationDate
		existingSubscription.IsActive = true
		existingSubscription.AutoRenewStatus = true
		existingSubscription.TransactionID = notification.TransactionID
		err = s.repository.UpdateSubscription(existingSubscription)
	} else {
		// Create new subscription
		subscription := &entities.Subscription{
			UserID:                userID,
			RevenueCatCustomerID:  revenueCatCustomerID,
			ProductID:             notification.ProductID,
			OriginalTransactionID: notification.OriginalTransactionID,
			TransactionID:         notification.TransactionID,
			PurchaseDate:          notification.PurchaseDate,
			ExpiresDate:           notification.ExpirationDate,
			IsActive:              true,
			AutoRenewStatus:       true,
			Environment:           "PRODUCTION", // Default to production
			Store:                 "APP_STORE",  // Default to App Store
		}
		err = s.repository.CreateSubscription(subscription)
	}

	if err != nil {
		return err
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(userID)
}
