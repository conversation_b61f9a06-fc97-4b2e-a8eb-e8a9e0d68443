import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Alert,
  ScrollView,
  StatusBar,
  Linking
} from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useRevenueCat } from '../../providers/RevenueCatProvider';
import { usePremium } from '../../context/PremiumContext';
import { useAuth } from '../../context/AuthContext';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { spacing } from '../../theme/spacing';
import { typography } from '../../theme/typography';
import Button from '../../components/common/Button';

type RootStackParamList = {
  Menu: undefined;
  home: undefined;
  Login: undefined;
};

export default function PaywallScreen() {
  const { offerings, purchasePackage, restorePurchases, customerInfo } = useRevenueCat();
  const { checkPremiumStatus } = usePremium();
  const { state: authState } = useAuth();
  const [monthlyPkg, setMonthlyPkg] = useState<any>(null);
  const [yearlyPkg, setYearlyPkg] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [restoreLoading, setRestoreLoading] = useState(false);

  const colors = useThemedColors();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  useEffect(() => {
    console.log('PaywallScreen loaded');
    console.log('Offerings:', offerings);
    if (offerings?.current?.availablePackages?.length) {
      console.log('Available packages:', offerings.current.availablePackages);

      // Aylık paketi bul (eskiden monthly olan şimdi aylık olacak)
      const monthlyFound = offerings.current.availablePackages.find(
        (p: any) => p.product.identifier === 'com.butce360.app.one.month'
      );
      setMonthlyPkg(monthlyFound);

      // Yıllık paketi bul (eskiden yearly olan şimdi yıllık olacak ve önerilen olacak)
      const yearlyFound = offerings.current.availablePackages.find(
        (p: any) => p.product.identifier === 'com.butce360.app.one.year'
      );
      setYearlyPkg(yearlyFound);
    }
  }, [offerings]);

  const handlePurchase = async (packageType: 'monthly' | 'yearly') => {
    // Check if user is authenticated (not guest)
    if (authState.isGuest || !authState.isAuthenticated) {
      Alert.alert(
        'Giriş Gerekli',
        'Abonelik satın almak için önce giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => navigation.navigate('Login') }
        ]
      );
      return;
    }

    const pkg = packageType === 'monthly' ? monthlyPkg : yearlyPkg;
    if (!pkg) {
      Alert.alert('Ürün bulunamadı', `${packageType === 'monthly' ? 'Aylık' : 'Yıllık'} paket RevenueCat tarafında gelmedi.`);
      return;
    }
    setLoading(true);
    const result = await purchasePackage(pkg);
    setLoading(false);

    if (result.success) {
      // Refresh premium status after successful purchase
      await checkPremiumStatus();

      // Wait a bit for backend to process the purchase notification
      setTimeout(async () => {
        await checkPremiumStatus();
      }, 2000);

      Alert.alert('Başarılı 🎉', 'Abonelik aktif edildi!', [
        { text: 'Tamam', onPress: () => navigation.navigate('Menu') }
      ]);
    } else if (result.cancelled) {
      Alert.alert('İptal edildi', 'Kullanıcı satın almayı iptal etti.');
    } else {
      Alert.alert('Hata', result.error?.message || 'Bilinmeyen hata');
    }
  };

  const handleRestorePurchases = async () => {
    setRestoreLoading(true);

    try {
      const result = await restorePurchases();

      if (result.success) {
        // Refresh premium status after successful restore
        await checkPremiumStatus();

        // Check if user now has premium access
        const hasActivePremium = !!result.customer?.entitlements?.active?.premium;

        if (hasActivePremium) {
          Alert.alert('Başarılı 🎉', 'Satın alımlarınız başarıyla geri yüklendi! Premium özellikleriniz aktif edildi.', [
            { text: 'Tamam', onPress: () => navigation.navigate('Menu') }
          ]);
        } else {
          Alert.alert(
            'Bilgi ℹ️',
            'Geri yükleme işlemi tamamlandı ancak bu Apple/Google hesabı için aktif premium abonelik bulunamadı.\n\nEğer daha önce satın aldıysanız, satın alma işlemini yaptığınız hesapla giriş yaptığınızdan emin olun.',
            [{ text: 'Tamam' }]
          );
        }
      } else {
        // Handle specific error cases
        const errorMessage = result.error?.message || 'Bilinmeyen hata';

        if (errorMessage.includes('cancelled') || errorMessage.includes('user cancelled')) {
          Alert.alert('İptal Edildi', 'Geri yükleme işlemi iptal edildi.');
        } else if (errorMessage.includes('network') || errorMessage.includes('internet')) {
          Alert.alert('Bağlantı Hatası', 'İnternet bağlantınızı kontrol edip tekrar deneyin.');
        } else {
          Alert.alert(
            'Hata ❌',
            `Satın alımlar geri yüklenirken hata oluştu:\n\n${errorMessage}\n\nLütfen daha sonra tekrar deneyin.`,
            [{ text: 'Tamam' }]
          );
        }
      }
    } catch (error: any) {
      console.error('[PaywallScreen] Restore purchases error:', error);
      Alert.alert(
        'Beklenmeyen Hata',
        'Geri yükleme işlemi sırasında beklenmeyen bir hata oluştu. Lütfen uygulamayı yeniden başlatıp tekrar deneyin.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setRestoreLoading(false);
    }
  };

  // Aktif entitlement var mı kontrol et (RevenueCat dashboard'daki entitlement adına göre)
  const isPremiumActive = !!customerInfo?.entitlements?.active?.premium;

  const styles = createStyles(colors);

  return (
    <View
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Premium Benefits Section */}
        <View style={styles.benefitsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Premium Özellikleri
          </Text>
          <View
            style={[
              styles.benefitsCard,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>📈</Text>
              <Text
                style={[styles.benefitText, { color: colors.text.primary }]}
              >
                Yatırımlarınızı akıllıca karşılaştırın
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>📑</Text>
              <Text
                style={[styles.benefitText, { color: colors.text.primary }]}
              >
                Kredi kartı ekstrenizden işlemleri otomatik çıkarın
              </Text>
            </View>
          </View>
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
            <Text
              style={[styles.loadingText, { color: colors.text.secondary }]}
            >
              Paketler yükleniyor...
            </Text>
          </View>
        )}

        {!loading && (monthlyPkg || yearlyPkg) && (
          <View style={styles.packagesSection}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Abonelik Seçenekleri
            </Text>

            {/* Paketleri yan yana göster */}
            <View style={styles.packagesRow}>
              {monthlyPkg && (
                <View
                  style={[
                    styles.packageCard,
                    styles.packageCardHalf,
                    { backgroundColor: colors.background.secondary },
                  ]}
                >
                  <View style={styles.packageContent}>
                    <Text
                      style={[styles.packageTitle, { color: colors.text.primary }]}
                    >
                      Aylık Plan
                    </Text>
                    <Text
                      style={[styles.packagePrice, { color: colors.primary[500] }]}
                    >
                      {monthlyPkg.product.priceString}
                    </Text>
                    <Text
                      style={[
                        styles.packageDescription,
                        { color: colors.text.secondary },
                      ]}
                    >
                      Aylık Plan
                    </Text>
                  </View>
                  <Button
                    title={`Abone Ol ${monthlyPkg.product.priceString} / Aylık`}
                    onPress={() => handlePurchase('monthly')}
                    variant="outline"
                    fullWidth
                    disabled={loading}
                    style={styles.purchaseButton}
                  />
                </View>
              )}

              {yearlyPkg && (
                <View
                  style={[
                    styles.packageCard,
                    styles.packageCardHalf,
                    styles.recommendedCard,
                    { backgroundColor: colors.background.secondary },
                  ]}
                >
                  <View style={styles.recommendedBadge}>
                    <Text style={styles.recommendedText}>ÖNERİLEN</Text>
                  </View>
                  <View style={styles.packageContent}>
                    <Text
                      style={[styles.packageTitle, { color: colors.text.primary }]}
                    >
                      Yıllık Plan
                    </Text>
                    <Text
                      style={[styles.packagePrice, { color: colors.primary[500] }]}
                    >
                      {yearlyPkg.product.priceString}
                    </Text>
                    <Text
                      style={[
                        styles.packageDescription,
                        { color: colors.text.secondary },
                      ]}
                    >
                      Yıllık Plan - En Avantajlı
                    </Text>
                  </View>
                  <Button
                    title={`Abone Ol ${yearlyPkg.product.priceString} / Yıllık`}
                    onPress={() => handlePurchase('yearly')}
                    variant="primary"
                    fullWidth
                    disabled={loading}
                    style={styles.purchaseButton}
                  />
                </View>
              )}
            </View>

            {/* Terms of Service */}
            <View style={styles.termsContainer}>
              <Text style={[styles.termsText, { color: colors.text.secondary }]}>
                Abone olarak{' '}
                <Text
                  style={[styles.linkText, { color: colors.primary[500] }]}
                  onPress={() => Linking.openURL('https://butce360.com/kullanim-sartlari')}
                >
                  Kullanım Şartları
                </Text>
                'nı kabul etmiş olursun. Abonelikler, iptal edilene kadar otomatik olarak yenilenir. Daha önce satın aldıysanız{' '}
                <Text
                  style={[styles.linkText, { color: colors.primary[500] }]}
                  onPress={handleRestorePurchases}
                >
                  Satın Alımları Geri Yükle
                </Text>
                {' '}yapabilirsiniz. Aboneliğini, abone olduğun platform üzerinden yönet.
                {restoreLoading && (
                  <Text style={[styles.restoreLoadingText, { color: colors.text.secondary }]}>
                    {'\n'}Satın alımlar geri yükleniyor...
                  </Text>
                )}
              </Text>
            </View>
          </View>
        )}

        {!loading && !monthlyPkg && !yearlyPkg && (
          <View
            style={[
              styles.errorCard,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <Text style={[styles.errorText, { color: colors.error[500] }]}>
              Ürünler getirilirken sorun yaşandı!
            </Text>
          </View>
        )}

        {/* Status Section */}
        <View
          style={[
            styles.statusCard,
            { backgroundColor: colors.background.secondary },
          ]}
        >
          <Text style={[styles.statusTitle, { color: colors.text.primary }]}>
            Abonelik Durumu
          </Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusIcon}>
              {isPremiumActive ? '✅' : '❌'}
            </Text>
            <Text
              style={[
                styles.statusText,
                {
                  color: isPremiumActive
                    ? colors.success[500]
                    : colors.text.secondary,
                },
              ]}
            >
              {isPremiumActive ? 'Premium Aktif' : 'Premium Pasif'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },



  // ScrollView
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.screenPadding,
    paddingBottom: spacing['4xl'],
  },

  // Benefits Section
  benefitsSection: {
    marginBottom: spacing['3xl'],
  },
  sectionTitle: {
    ...typography.styles.h4,
    marginBottom: spacing.lg,
    marginTop: spacing.xl,
  },
  benefitsCard: {
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  benefitIcon: {
    fontSize: 20,
    marginRight: spacing.md,
    width: 24,
  },
  benefitText: {
    ...typography.styles.body,
    flex: 1,
  },

  // Loading
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  loadingText: {
    ...typography.styles.body2,
    marginTop: spacing.md,
  },

  // Packages Section
  packagesSection: {
    marginBottom: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  packagesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.md,
  },
  packageCard: {
    backgroundColor: colors.surface.primary,
    borderRadius: spacing.cardRadius,
    padding: spacing.xl,
    marginBottom: spacing.lg,
    alignItems: 'center',
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    justifyContent: 'space-between',
    minHeight: 200,
  },
  packageCardHalf: {
    flex: 1,
    marginBottom: 0,
  },
  packageContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recommendedCard: {
    borderWidth: 2,
    borderColor: colors.primary[500],
    position: 'relative',
  },
  recommendedBadge: {
    position: 'absolute',
    top: -8,
    backgroundColor: colors.primary[500],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: spacing.xs,
  },
  recommendedText: {
    ...typography.styles.caption,
    color: colors.surface.primary,
    fontWeight: '600',
    fontSize: 10,
  },
  packageTitle: {
    ...typography.styles.h5,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  packagePrice: {
    ...typography.styles.currencyLarge,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  packageDescription: {
    ...typography.styles.body2,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  purchaseButton: {
    marginTop: 'auto',
    width: '100%',
  },

  // Error
  errorCard: {
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  errorText: {
    ...typography.styles.body,
    textAlign: 'center',
  },

  // Status
  statusCard: {
    padding: spacing.xl,
    alignItems: 'center',
    borderRadius: spacing.cardRadius,
  },
  statusTitle: {
    ...typography.styles.h6,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    fontSize: 20,
    marginRight: spacing.sm,
  },
  statusText: {
    ...typography.styles.body,
  },

  // Terms of Service
  termsContainer: {
    marginTop: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  termsText: {
    ...typography.styles.caption,
    textAlign: 'center',
    lineHeight: 18,
  },
  linkText: {
    ...typography.styles.caption,
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
  restoreLoadingText: {
    ...typography.styles.caption,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
